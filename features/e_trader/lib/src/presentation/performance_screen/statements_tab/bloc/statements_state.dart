part of 'statements_bloc.dart';

@freezed
sealed class StatementsState with _$StatementsState {
  factory StatementsState({
    @Default(StatementsProcessState.loading())
    StatementsProcessState processState,
    @Default([]) List<StatementItem> statements,
    @Default(SortOrder.descending) SortOrder sortOrder,
    @Default(1) int currentPage,
    @Default(false) bool hasReachedMax,
    @Default(0) int statementsCount,
    String? accountNumber,
    String? creationDate,
    DateTime? fromDate,
    DateTime? toDate,
    @Default(false) bool hasFilters,
  }) = _StatementsState;
}

@freezed
sealed class StatementsProcessState with _$StatementsProcessState {
  const factory StatementsProcessState.loading() = StatementsLoadingState;
  const factory StatementsProcessState.success() = StatementsSuccessState;
  const factory StatementsProcessState.emptyFilterResult() =
      StatementsEmptyFilterResultProcessState;
  const factory StatementsProcessState.error(Exception e) =
      StatementsErrorState;
}
