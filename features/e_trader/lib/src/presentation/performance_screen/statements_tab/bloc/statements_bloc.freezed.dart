// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'statements_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$StatementsEvent {

 SortOrder? get sortOrder; DateTime? get fromDate; DateTime? get toDate; bool? get isStartFromPageOne; bool? get hasFilters;
/// Create a copy of StatementsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StatementsEventCopyWith<StatementsEvent> get copyWith => _$StatementsEventCopyWithImpl<StatementsEvent>(this as StatementsEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsEvent&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.fromDate, fromDate) || other.fromDate == fromDate)&&(identical(other.toDate, toDate) || other.toDate == toDate)&&(identical(other.isStartFromPageOne, isStartFromPageOne) || other.isStartFromPageOne == isStartFromPageOne)&&(identical(other.hasFilters, hasFilters) || other.hasFilters == hasFilters));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder,fromDate,toDate,isStartFromPageOne,hasFilters);

@override
String toString() {
  return 'StatementsEvent(sortOrder: $sortOrder, fromDate: $fromDate, toDate: $toDate, isStartFromPageOne: $isStartFromPageOne, hasFilters: $hasFilters)';
}


}

/// @nodoc
abstract mixin class $StatementsEventCopyWith<$Res>  {
  factory $StatementsEventCopyWith(StatementsEvent value, $Res Function(StatementsEvent) _then) = _$StatementsEventCopyWithImpl;
@useResult
$Res call({
 SortOrder? sortOrder, DateTime? fromDate, DateTime? toDate, bool? isStartFromPageOne, bool? hasFilters
});




}
/// @nodoc
class _$StatementsEventCopyWithImpl<$Res>
    implements $StatementsEventCopyWith<$Res> {
  _$StatementsEventCopyWithImpl(this._self, this._then);

  final StatementsEvent _self;
  final $Res Function(StatementsEvent) _then;

/// Create a copy of StatementsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sortOrder = freezed,Object? fromDate = freezed,Object? toDate = freezed,Object? isStartFromPageOne = freezed,Object? hasFilters = freezed,}) {
  return _then(_self.copyWith(
sortOrder: freezed == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder?,fromDate: freezed == fromDate ? _self.fromDate : fromDate // ignore: cast_nullable_to_non_nullable
as DateTime?,toDate: freezed == toDate ? _self.toDate : toDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isStartFromPageOne: freezed == isStartFromPageOne ? _self.isStartFromPageOne : isStartFromPageOne // ignore: cast_nullable_to_non_nullable
as bool?,hasFilters: freezed == hasFilters ? _self.hasFilters : hasFilters // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// @nodoc


class _FetchStatements implements StatementsEvent {
  const _FetchStatements({this.sortOrder, this.fromDate, this.toDate, this.isStartFromPageOne, this.hasFilters});
  

@override final  SortOrder? sortOrder;
@override final  DateTime? fromDate;
@override final  DateTime? toDate;
@override final  bool? isStartFromPageOne;
@override final  bool? hasFilters;

/// Create a copy of StatementsEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FetchStatementsCopyWith<_FetchStatements> get copyWith => __$FetchStatementsCopyWithImpl<_FetchStatements>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FetchStatements&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.fromDate, fromDate) || other.fromDate == fromDate)&&(identical(other.toDate, toDate) || other.toDate == toDate)&&(identical(other.isStartFromPageOne, isStartFromPageOne) || other.isStartFromPageOne == isStartFromPageOne)&&(identical(other.hasFilters, hasFilters) || other.hasFilters == hasFilters));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder,fromDate,toDate,isStartFromPageOne,hasFilters);

@override
String toString() {
  return 'StatementsEvent.fetchStatements(sortOrder: $sortOrder, fromDate: $fromDate, toDate: $toDate, isStartFromPageOne: $isStartFromPageOne, hasFilters: $hasFilters)';
}


}

/// @nodoc
abstract mixin class _$FetchStatementsCopyWith<$Res> implements $StatementsEventCopyWith<$Res> {
  factory _$FetchStatementsCopyWith(_FetchStatements value, $Res Function(_FetchStatements) _then) = __$FetchStatementsCopyWithImpl;
@override @useResult
$Res call({
 SortOrder? sortOrder, DateTime? fromDate, DateTime? toDate, bool? isStartFromPageOne, bool? hasFilters
});




}
/// @nodoc
class __$FetchStatementsCopyWithImpl<$Res>
    implements _$FetchStatementsCopyWith<$Res> {
  __$FetchStatementsCopyWithImpl(this._self, this._then);

  final _FetchStatements _self;
  final $Res Function(_FetchStatements) _then;

/// Create a copy of StatementsEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sortOrder = freezed,Object? fromDate = freezed,Object? toDate = freezed,Object? isStartFromPageOne = freezed,Object? hasFilters = freezed,}) {
  return _then(_FetchStatements(
sortOrder: freezed == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder?,fromDate: freezed == fromDate ? _self.fromDate : fromDate // ignore: cast_nullable_to_non_nullable
as DateTime?,toDate: freezed == toDate ? _self.toDate : toDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isStartFromPageOne: freezed == isStartFromPageOne ? _self.isStartFromPageOne : isStartFromPageOne // ignore: cast_nullable_to_non_nullable
as bool?,hasFilters: freezed == hasFilters ? _self.hasFilters : hasFilters // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

/// @nodoc
mixin _$StatementsState {

 StatementsProcessState get processState; List<StatementItem> get statements; SortOrder get sortOrder; int get currentPage; bool get hasReachedMax; int get statementsCount; String? get accountNumber; String? get creationDate; DateTime? get fromDate; DateTime? get toDate; bool get hasFilters;
/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StatementsStateCopyWith<StatementsState> get copyWith => _$StatementsStateCopyWithImpl<StatementsState>(this as StatementsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other.statements, statements)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.hasReachedMax, hasReachedMax) || other.hasReachedMax == hasReachedMax)&&(identical(other.statementsCount, statementsCount) || other.statementsCount == statementsCount)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.creationDate, creationDate) || other.creationDate == creationDate)&&(identical(other.fromDate, fromDate) || other.fromDate == fromDate)&&(identical(other.toDate, toDate) || other.toDate == toDate)&&(identical(other.hasFilters, hasFilters) || other.hasFilters == hasFilters));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(statements),sortOrder,currentPage,hasReachedMax,statementsCount,accountNumber,creationDate,fromDate,toDate,hasFilters);

@override
String toString() {
  return 'StatementsState(processState: $processState, statements: $statements, sortOrder: $sortOrder, currentPage: $currentPage, hasReachedMax: $hasReachedMax, statementsCount: $statementsCount, accountNumber: $accountNumber, creationDate: $creationDate, fromDate: $fromDate, toDate: $toDate, hasFilters: $hasFilters)';
}


}

/// @nodoc
abstract mixin class $StatementsStateCopyWith<$Res>  {
  factory $StatementsStateCopyWith(StatementsState value, $Res Function(StatementsState) _then) = _$StatementsStateCopyWithImpl;
@useResult
$Res call({
 StatementsProcessState processState, List<StatementItem> statements, SortOrder sortOrder, int currentPage, bool hasReachedMax, int statementsCount, String? accountNumber, String? creationDate, DateTime? fromDate, DateTime? toDate, bool hasFilters
});


$StatementsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$StatementsStateCopyWithImpl<$Res>
    implements $StatementsStateCopyWith<$Res> {
  _$StatementsStateCopyWithImpl(this._self, this._then);

  final StatementsState _self;
  final $Res Function(StatementsState) _then;

/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? statements = null,Object? sortOrder = null,Object? currentPage = null,Object? hasReachedMax = null,Object? statementsCount = null,Object? accountNumber = freezed,Object? creationDate = freezed,Object? fromDate = freezed,Object? toDate = freezed,Object? hasFilters = null,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as StatementsProcessState,statements: null == statements ? _self.statements : statements // ignore: cast_nullable_to_non_nullable
as List<StatementItem>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,statementsCount: null == statementsCount ? _self.statementsCount : statementsCount // ignore: cast_nullable_to_non_nullable
as int,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,creationDate: freezed == creationDate ? _self.creationDate : creationDate // ignore: cast_nullable_to_non_nullable
as String?,fromDate: freezed == fromDate ? _self.fromDate : fromDate // ignore: cast_nullable_to_non_nullable
as DateTime?,toDate: freezed == toDate ? _self.toDate : toDate // ignore: cast_nullable_to_non_nullable
as DateTime?,hasFilters: null == hasFilters ? _self.hasFilters : hasFilters // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$StatementsProcessStateCopyWith<$Res> get processState {
  
  return $StatementsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _StatementsState implements StatementsState {
   _StatementsState({this.processState = const StatementsProcessState.loading(), final  List<StatementItem> statements = const [], this.sortOrder = SortOrder.descending, this.currentPage = 1, this.hasReachedMax = false, this.statementsCount = 0, this.accountNumber, this.creationDate, this.fromDate, this.toDate, this.hasFilters = false}): _statements = statements;
  

@override@JsonKey() final  StatementsProcessState processState;
 final  List<StatementItem> _statements;
@override@JsonKey() List<StatementItem> get statements {
  if (_statements is EqualUnmodifiableListView) return _statements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_statements);
}

@override@JsonKey() final  SortOrder sortOrder;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  bool hasReachedMax;
@override@JsonKey() final  int statementsCount;
@override final  String? accountNumber;
@override final  String? creationDate;
@override final  DateTime? fromDate;
@override final  DateTime? toDate;
@override@JsonKey() final  bool hasFilters;

/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StatementsStateCopyWith<_StatementsState> get copyWith => __$StatementsStateCopyWithImpl<_StatementsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StatementsState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other._statements, _statements)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.hasReachedMax, hasReachedMax) || other.hasReachedMax == hasReachedMax)&&(identical(other.statementsCount, statementsCount) || other.statementsCount == statementsCount)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.creationDate, creationDate) || other.creationDate == creationDate)&&(identical(other.fromDate, fromDate) || other.fromDate == fromDate)&&(identical(other.toDate, toDate) || other.toDate == toDate)&&(identical(other.hasFilters, hasFilters) || other.hasFilters == hasFilters));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(_statements),sortOrder,currentPage,hasReachedMax,statementsCount,accountNumber,creationDate,fromDate,toDate,hasFilters);

@override
String toString() {
  return 'StatementsState(processState: $processState, statements: $statements, sortOrder: $sortOrder, currentPage: $currentPage, hasReachedMax: $hasReachedMax, statementsCount: $statementsCount, accountNumber: $accountNumber, creationDate: $creationDate, fromDate: $fromDate, toDate: $toDate, hasFilters: $hasFilters)';
}


}

/// @nodoc
abstract mixin class _$StatementsStateCopyWith<$Res> implements $StatementsStateCopyWith<$Res> {
  factory _$StatementsStateCopyWith(_StatementsState value, $Res Function(_StatementsState) _then) = __$StatementsStateCopyWithImpl;
@override @useResult
$Res call({
 StatementsProcessState processState, List<StatementItem> statements, SortOrder sortOrder, int currentPage, bool hasReachedMax, int statementsCount, String? accountNumber, String? creationDate, DateTime? fromDate, DateTime? toDate, bool hasFilters
});


@override $StatementsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$StatementsStateCopyWithImpl<$Res>
    implements _$StatementsStateCopyWith<$Res> {
  __$StatementsStateCopyWithImpl(this._self, this._then);

  final _StatementsState _self;
  final $Res Function(_StatementsState) _then;

/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? statements = null,Object? sortOrder = null,Object? currentPage = null,Object? hasReachedMax = null,Object? statementsCount = null,Object? accountNumber = freezed,Object? creationDate = freezed,Object? fromDate = freezed,Object? toDate = freezed,Object? hasFilters = null,}) {
  return _then(_StatementsState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as StatementsProcessState,statements: null == statements ? _self._statements : statements // ignore: cast_nullable_to_non_nullable
as List<StatementItem>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,statementsCount: null == statementsCount ? _self.statementsCount : statementsCount // ignore: cast_nullable_to_non_nullable
as int,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,creationDate: freezed == creationDate ? _self.creationDate : creationDate // ignore: cast_nullable_to_non_nullable
as String?,fromDate: freezed == fromDate ? _self.fromDate : fromDate // ignore: cast_nullable_to_non_nullable
as DateTime?,toDate: freezed == toDate ? _self.toDate : toDate // ignore: cast_nullable_to_non_nullable
as DateTime?,hasFilters: null == hasFilters ? _self.hasFilters : hasFilters // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of StatementsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$StatementsProcessStateCopyWith<$Res> get processState {
  
  return $StatementsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$StatementsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'StatementsProcessState()';
}


}

/// @nodoc
class $StatementsProcessStateCopyWith<$Res>  {
$StatementsProcessStateCopyWith(StatementsProcessState _, $Res Function(StatementsProcessState) __);
}


/// @nodoc


class StatementsLoadingState implements StatementsProcessState {
  const StatementsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'StatementsProcessState.loading()';
}


}




/// @nodoc


class StatementsSuccessState implements StatementsProcessState {
  const StatementsSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'StatementsProcessState.success()';
}


}




/// @nodoc


class StatementsEmptyFilterResultProcessState implements StatementsProcessState {
  const StatementsEmptyFilterResultProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsEmptyFilterResultProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'StatementsProcessState.emptyFilterResult()';
}


}




/// @nodoc


class StatementsErrorState implements StatementsProcessState {
  const StatementsErrorState(this.e);
  

 final  Exception e;

/// Create a copy of StatementsProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StatementsErrorStateCopyWith<StatementsErrorState> get copyWith => _$StatementsErrorStateCopyWithImpl<StatementsErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StatementsErrorState&&(identical(other.e, e) || other.e == e));
}


@override
int get hashCode => Object.hash(runtimeType,e);

@override
String toString() {
  return 'StatementsProcessState.error(e: $e)';
}


}

/// @nodoc
abstract mixin class $StatementsErrorStateCopyWith<$Res> implements $StatementsProcessStateCopyWith<$Res> {
  factory $StatementsErrorStateCopyWith(StatementsErrorState value, $Res Function(StatementsErrorState) _then) = _$StatementsErrorStateCopyWithImpl;
@useResult
$Res call({
 Exception e
});




}
/// @nodoc
class _$StatementsErrorStateCopyWithImpl<$Res>
    implements $StatementsErrorStateCopyWith<$Res> {
  _$StatementsErrorStateCopyWithImpl(this._self, this._then);

  final StatementsErrorState _self;
  final $Res Function(StatementsErrorState) _then;

/// Create a copy of StatementsProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? e = null,}) {
  return _then(StatementsErrorState(
null == e ? _self.e : e // ignore: cast_nullable_to_non_nullable
as Exception,
  ));
}


}

// dart format on
