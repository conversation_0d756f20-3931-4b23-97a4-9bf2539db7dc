import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/bloc/statements_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/widgets/statement_tile.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/widgets/statement_viewer.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/widgets/statements_loading.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/widgets/statements_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class StatementsTab extends StatefulWidget {
  const StatementsTab({super.key});

  @override
  State<StatementsTab> createState() => _StatementsTabState();
}

class _StatementsTabState extends State<StatementsTab> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Scaffold(
      body: BlocProvider(
        create:
            (_) =>
                diContainer<StatementsBloc>()
                  ..add(StatementsEvent.fetchStatements()),
        child: Builder(
          builder: (blocContext) {
            return BlocConsumer<StatementsBloc, StatementsState>(
              buildWhen: (previous, current) => previous != current,
              listener: (BuildContext listenerContext, StatementsState state) {
                if (state.processState is StatementsErrorState) {
                  Future.microtask(() {
                    final toast = DuploToast();
                    toast.hidesToastMessage();
                    toast.showToastMessage(
                      autoCloseDuration: const Duration(seconds: 3),
                      context: listenerContext,
                      widget: DuploToastMessage(
                        titleMessage: localization.trader_failedToLoad,
                        descriptionMessage:
                            localization.trader_failedToLoadDescription,
                        messageType: ToastMessageType.error,
                        onLeadingAction: () => toast.hidesToastMessage(),
                        actionButtonTitle: localization.trader_reload,
                        onTap: () {
                          toast.hidesToastMessage();
                          // Use the correct context that has access to the bloc
                          blocContext.read<StatementsBloc>().add(
                            StatementsEvent.fetchStatements(),
                          );
                        },
                      ),
                    );
                  });
                }
              },
              builder: (blocBuilderContext, state) {
                return switch (state.processState) {
                  StatementsErrorState() => Container(
                    color: theme.background.bgPrimary,
                  ),
                  StatementsEmptyFilterResultProcessState() => Container(
                    color: theme.background.bgPrimary,
                    child: SafeArea(
                      child: Column(
                        children: [
                          const SizedBox(height: 12),
                          Material(
                            color: Colors.transparent,
                            child: GroupedButtonsWidget(
                              groupedButtonsItemList: [
                                GroupedButtonsItem(
                                  semanticsIdentifier:
                                      "grouped_buttons_sort_statements",
                                  title: localization.trader_sort,
                                  buttonIcon: trader.Assets.images.sort.svg(),
                                  onTap: () {
                                    StatementsBottomSheet.showSort(
                                      blocBuilderContext,
                                    );
                                  },
                                ),
                                GroupedButtonsItem(
                                  isSelected: state.hasFilters,
                                  semanticsIdentifier:
                                      "grouped_buttons_filter_statements",
                                  title: localization.trader_filter,
                                  buttonIcon: trader.Assets.images.filter.svg(),
                                  onTap: () {
                                    StatementsBottomSheet.showFilter(
                                      blocBuilderContext,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Expanded(
                            child: Center(
                              child: EmptyOrErrorStateComponent.empty(
                                svgImage:
                                    trader.Assets.images.emptyStatement.svg(),
                                title: localization.trader_noStatements,
                                description:
                                    localization.trader_noStatementsDescription,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  _ => Container(
                    color: theme.background.bgPrimary,
                    child: SafeArea(
                      child: Column(
                        children: [
                          (state.statements.isNotEmpty &&
                                      state.processState ==
                                          StatementsProcessState.success()) ||
                                  state.processState ==
                                      StatementsProcessState.loading()
                              ? Column(
                                children: [
                                  const SizedBox(height: 12),
                                  Material(
                                    color: Colors.transparent,
                                    child: GroupedButtonsWidget(
                                      groupedButtonsItemList: [
                                        GroupedButtonsItem(
                                          semanticsIdentifier:
                                              "grouped_buttons_sort_statements",
                                          title: localization.trader_sort,
                                          buttonIcon:
                                              trader.Assets.images.sort.svg(),
                                          onTap: () {
                                            StatementsBottomSheet.showSort(
                                              blocBuilderContext,
                                            );
                                          },
                                        ),
                                        GroupedButtonsItem(
                                          isSelected: state.hasFilters,
                                          semanticsIdentifier:
                                              "grouped_buttons_filter_statements",
                                          title: localization.trader_filter,
                                          buttonIcon:
                                              trader.Assets.images.filter.svg(),
                                          onTap: () {
                                            StatementsBottomSheet.showFilter(
                                              blocBuilderContext,
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                              : const SizedBox(),
                          const SizedBox(height: 16),
                          Expanded(
                            child: PagedView.list(
                              scrollController: _scrollController,
                              physics: switch (state.processState) {
                                StatementsErrorState() =>
                                  const NeverScrollableScrollPhysics(),
                                StatementsSuccessState() =>
                                  state.statements.isEmpty
                                      ? const NeverScrollableScrollPhysics()
                                      : const AlwaysScrollableScrollPhysics(),
                                _ => const AlwaysScrollableScrollPhysics(),
                              },
                              padding: EdgeInsets.zero,
                              itemCount: state.statements.length,
                              centerError: true,
                              centerLoading: true,
                              centerEmpty: true,
                              hasError: false,
                              isLoading: switch (state.processState) {
                                StatementsLoadingState() => true,
                                _ => false,
                              },
                              hasReachedMax:
                                  state.statementsCount ==
                                  state.statements.length,
                              emptyBuilder:
                                  (ctx) => EmptyOrErrorStateComponent.empty(
                                    svgImage:
                                        trader.Assets.images.emptyStatement
                                            .svg(),
                                    title: localization.trader_noStatements,
                                    description:
                                        localization
                                            .trader_noStatementsDescription,
                                  ),
                              loadingBuilder: (ctx) => StatementsLoading(),
                              errorBuilder: (ctx) => const Text("error"),
                              separatorBuilder:
                                  (ctx, index) => const SizedBox(),
                              onFetchData: () {
                                blocBuilderContext.read<StatementsBloc>().add(
                                  StatementsEvent.fetchStatements(),
                                );
                              },
                              itemBuilder: (ctx, index) {
                                return StatementTile(
                                  showDate:
                                      (index == 0 ||
                                          !areDatesOnSameDay(
                                            state.statements
                                                .elementAtOrNull(index)!
                                                .dateTime,
                                            state.statements
                                                .elementAtOrNull(index - 1)!
                                                .dateTime,
                                          )),
                                  statement:
                                      state.statements.elementAtOrNull(index)!,
                                  onTap: () {
                                    DuploSheet.showModalSheetV2<void>(
                                      ctx,
                                      appBar: DuploAppBar(
                                        title: localization.trader_statements,
                                        automaticallyImplyLeading: false,
                                        duploAppBarTextAlign:
                                            DuploAppBarTextAlign.left,
                                        actions: [
                                          IconButton(
                                            icon: Assets.images.closeIc.svg(),
                                            onPressed:
                                                () => Navigator.pop(context),
                                          ),
                                        ],
                                      ),
                                      bottomBar: SizedBox(height: 0, width: 0),
                                      content: StatementViewer(
                                        url:
                                            state.statements
                                                .elementAtOrNull(index)!
                                                .statementUrl ??
                                            "",
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 7),
                        ],
                      ),
                    ),
                  ),
                };
              },
            );
          },
        ),
      ),
    );
  }

  bool areDatesOnSameDay(DateTime? isoDate1, DateTime? isoDate2) {
    if (isoDate1 == null || isoDate2 == null) {
      return false;
    }
    return isoDate1.year == isoDate2.year &&
        isoDate1.month == isoDate2.month &&
        isoDate1.day == isoDate2.day;
  }
}
