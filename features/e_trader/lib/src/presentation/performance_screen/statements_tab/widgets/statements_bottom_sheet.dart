import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/bloc/statements_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/widgets/statements_filter_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class StatementsBottomSheet {
  static void showSort(BuildContext context) {
    final loc = EquitiLocalization.of(context);

    final existing = context.read<StatementsBloc>().state.sortOrder;
    final allSortOptions = <SelectionOptionModel>[];

    SelectionOptionModel selected = SelectionOptionModel(
      displayText: SortOrderOptions.defaultOption.displayAsNewestToOldest(loc),
      identifier: SortOrderOptions.defaultOption.indentifier(),
    );

    SortOrder.values.toList().reversed.forEach((element) {
      final model = SelectionOptionModel(
        displayText: element.displayAsNewestToOldest(loc),
        identifier: element.indentifier(),
      );
      if (model.identifier == existing.indentifier()) {
        selected = model;
      }
      allSortOptions.add(model);
    });

    final textSelection = TextSelectionComponentScreen(
      buttonTitle: loc.trader_sort,
      options: allSortOptions,
      pageTitle: loc.trader_sortStatementsBy,
      selected: selected,
      onSelection: (selectedOption) {
        final order = selectedOption.identifier.toSortOrder();
        if (order != existing) {
          context.read<StatementsBloc>().add(
            StatementsEvent.fetchStatements(
              sortOrder: order,
              isStartFromPageOne: true,
              fromDate: context.read<StatementsBloc>().state.fromDate,
              toDate: context.read<StatementsBloc>().state.toDate,
              hasFilters: context.read<StatementsBloc>().state.hasFilters,
            ),
          );
        }

        Navigator.pop(context);
      },
    );

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_sortStatementsBy,

        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: textSelection,
      bottomBar: SizedBox(height: 0, width: 0),
    );
  }

  static void showFilter(BuildContext context) {
    final statementsBloc = context.read<StatementsBloc>();
    final state = statementsBloc.state;
    final loc = EquitiLocalization.of(context);

    DateTime? selectedFromDate = state.fromDate;
    DateTime? selectedToDate = state.toDate;

    DuploSheet.showModalSheetV2<void>(
      context,

      appBar: DuploAppBar(
        title: loc.trader_filterStatementsBy,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      bottomBar: SizedBox(height: 0, width: 0),
      content: StatefulBuilder(
        builder: (statefulBuilderContext, setState) {
          return StatementsFilterBottomSheet(
            selectedFromDate: selectedFromDate,
            selectedToDate: selectedToDate,
            onFromDateChanged: (date) {
              setState(() {
                selectedFromDate = date;
                if (selectedToDate != null && date.isAfter(selectedToDate!)) {
                  selectedToDate = date;
                }
              });
            },
            onToDateChanged: (date) {
              setState(() {
                selectedToDate = date;
              });
            },
            onClearAll: () {
              setState(() {
                selectedFromDate = null;
                selectedToDate = null;
              });
            },
            onApplyFilters: () {
              final bool hasFilters =
                  selectedFromDate != null || selectedToDate != null;
              statementsBloc.add(
                StatementsEvent.fetchStatements(
                  fromDate: selectedFromDate,
                  toDate: selectedToDate,
                  sortOrder: state.sortOrder,
                  isStartFromPageOne: true,
                  hasFilters: hasFilters,
                ),
              );
              Navigator.pop(context);
            },
          );
        },
      ),
    );
  }
}
