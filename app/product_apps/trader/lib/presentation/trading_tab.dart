import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent tradingTab() {
  return DisplayableComponent(
    title: "Trading Tab",
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/trading/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/trading_tab/success.json',
                ),
              ],
            });
          return Scaffold(body: const TradingTab());
        },
      ),
      DisplayableComponent(
        title: 'Empty',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/trading/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/trading_tab/empty.json',
                ),
              ],
            });
          return Scaffold(body: const TradingTab());
        },
      ),
      DisplayableComponent(
        title: 'Error',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/trading/v2': [
                MockResponse(
                  code: 400,
                  bodyFilePath: 'resources/mocks/trading_tab/failure.json',
                ),
              ],
            });
          return Scaffold(body: const TradingTab());
        },
      ),
    ],
  );
}
