import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent fundingTab() {
  return DisplayableComponent(
    title: "Funding Tab",
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/funding_tab/success.json',
                ),
              ],
            });
          return Scaffold(body: const FundingTab());
        },
      ),
      DisplayableComponent(
        title: 'Empty',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/funding_tab/empty.json',
                ),
              ],
            });
          return Scaffold(body: const FundingTab());
        },
      ),
      DisplayableComponent(
        title: 'Error',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  code: 404,
                  bodyFilePath: 'resources/mocks/funding_tab/failure.json',
                ),
              ],
            });
          return Scaffold(body: const FundingTab());
        },
      ),
    ],
  );
}
